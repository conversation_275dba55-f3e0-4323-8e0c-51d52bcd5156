["tests/logger_test.py::TestLogger::test_audit_level_logging", "tests/logger_test.py::TestLogger::test_concurrent_logging", "tests/logger_test.py::TestLogger::test_invalid_verbosity", "tests/logger_test.py::TestLogger::test_log_decorator_error", "tests/logger_test.py::TestLogger::test_log_decorator_state_output", "tests/logger_test.py::TestLogger::test_log_decorator_success", "tests/logger_test.py::TestLogger::test_log_error", "tests/logger_test.py::TestLogger::test_log_rotation", "tests/logger_test.py::TestLogger::test_log_state_output", "tests/logger_test.py::TestLogger::test_log_success", "tests/logger_test.py::TestLogger::test_missing_optional_fields", "tests/logger_test.py::TestLogger::test_verbosity_filter", "tests/logger_test.py::TestLogger::test_verbosity_filtering"]