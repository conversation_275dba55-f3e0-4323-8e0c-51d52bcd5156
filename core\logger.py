import logging
import logging.handlers
import json
import os
import queue
import time
import inspect
import threading
import os.path
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import uuid

class StateOutput:
    """Mock StateOutput class for demonstration purposes."""
    def __init__(self, status: str, data: Any, metadata: Optional[Dict] = None):
        self.status = status
        self.data = data
        self.metadata = metadata or {}

class JsonFormatter(logging.Formatter):
    """Custom formatter for JSONL output."""
    def format(self, record):
        log_entry = {
            "timestamp": self.formatTime(record, "%Y-%m-%dT%H:%M:%SZ"),
            "session_id": getattr(record, "session_id", str(uuid.uuid4())),
            "state_id": getattr(record, "state_id", "unknown"),
            "layer": getattr(record, "layer", "unknown"),
            "step": getattr(record, "step", record.funcName if hasattr(record, "funcName") else "unknown"),
            "custom_module": getattr(record, "custom_module", record.module),
            "agent_name": getattr(record, "agent_name", record.name),
            "action": getattr(record, "action", record.funcName if hasattr(record, "funcName") else "unknown"),
            "input": getattr(record, "input_data", None),
            "output": getattr(record, "output_data", None),
            "status": getattr(record, "status", "success" if record.levelno < 40 else "fail"),
            "metrics": getattr(record, "metrics", {}),
            "reason": getattr(record, "reason", None) or (record.getMessage() if record.levelno >= 40 else None),
            "memory_layer": getattr(record, "memory_layer", None),
            "trace_id": getattr(record, "trace_id", str(uuid.uuid4())),
            "next_state_id": getattr(record, "next_state_id", None)
        }
        log_entry = {k: v for k, v in log_entry.items() if v is not None}
        return json.dumps(log_entry, ensure_ascii=False)

class Logger:
    """Unified JSONL logger wrapping Python's logging module."""
    
    def __init__(self, log_dir: str = "logs", verbosity: str = "info", max_bytes: int = 10**6, backup_count: int = 5):
        """Initialize logger with log directory, verbosity, and rotation settings."""
        self.log_dir = log_dir
        self.verbosity_levels = {
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warn": logging.WARNING,
            "error": logging.ERROR,
            "audit": logging.CRITICAL
        }
        self.verbosity = self.verbosity_levels.get(verbosity.lower(), logging.INFO)
        self.queue_listeners = []
        self.queue_lock = threading.Lock()
        self.record_count = { "conversations": 0, "metrics": 0, "errors": 0 }  # Track processed records

        os.makedirs(os.path.join(log_dir, "conversations"), exist_ok=True)
        os.makedirs(os.path.join(log_dir, "metrics"), exist_ok=True)
        os.makedirs(os.path.join(log_dir, "errors"), exist_ok=True)

        self.logger = logging.getLogger(f"voice_agent_{id(self)}")
        self.logger.handlers.clear()
        self.logger.setLevel(self.verbosity)

        console_handler = logging.StreamHandler()
        console_handler.setFormatter(JsonFormatter())
        console_handler.setLevel(logging.DEBUG)
        self.logger.addHandler(console_handler)

        formatter = JsonFormatter()

        conv_queue = queue.Queue(-1)
        conv_rotating_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, "conversations", "conversations.jsonl"),
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        conv_rotating_handler.setLevel(logging.INFO)
        conv_rotating_handler.setFormatter(formatter)
        conv_handler = logging.handlers.QueueHandler(conv_queue)
        conv_handler.setLevel(logging.INFO)
        conv_handler.setFormatter(formatter)
        conv_handler.handler = conv_rotating_handler
        self.logger.addHandler(conv_handler)

        metrics_queue = queue.Queue(-1)
        metrics_rotating_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, "metrics", "metrics.jsonl"),
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        metrics_rotating_handler.setLevel(logging.INFO)
        metrics_rotating_handler.setFormatter(formatter)
        metrics_handler = logging.handlers.QueueHandler(metrics_queue)
        metrics_handler.setLevel(logging.INFO)
        metrics_handler.setFormatter(formatter)
        metrics_handler.handler = metrics_rotating_handler
        self.logger.addHandler(metrics_handler)

        errors_queue = queue.Queue(-1)
        error_rotating_handler = logging.handlers.RotatingFileHandler(
            os.path.join(log_dir, "errors", "errors.jsonl"),
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        error_rotating_handler.setLevel(logging.ERROR)
        error_rotating_handler.setFormatter(formatter)
        error_handler = logging.handlers.QueueHandler(errors_queue)
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        error_handler.handler = error_rotating_handler
        self.logger.addHandler(error_handler)

        self._start_queue_listeners([conv_handler, metrics_handler, error_handler])

    def _start_queue_listeners(self, queue_handlers):
        """Start queue listeners for each QueueHandler."""
        self.queue_listeners = []
        for handler in queue_handlers:
            try:
                # Custom QueueListener to track processed records
                class CountingQueueListener(logging.handlers.QueueListener):
                    def __init__(self, queue, *handlers, log_type):
                        super().__init__(queue, *handlers, respect_handler_level=True)
                        self.log_type = log_type

                    def handle(self, record):
                        super().handle(record)
                        Logger.this_instance.record_count[self.log_type] += 1

                CountingQueueListener.this_instance = self
                listener = CountingQueueListener(
                    handler.queue,
                    handler.handler,
                    log_type=handler.handler.baseFilename.split(os.sep)[-2]
                )
                listener.start()
                self.queue_listeners.append(listener)
                self.logger.debug(f"Started QueueListener for {handler.handler.baseFilename}, thread: {listener._thread.name if listener._thread else 'None'}")
            except Exception as e:
                self.logger.error(f"Failed to start QueueListener for {handler.handler.baseFilename}: {str(e)}")

    def log(
        self,
        level: str,
        message: str,
        session_id: str,
        state_id: str,
        layer: str,
        step: str,
        custom_module: str,
        agent_name: str,
        action: str,
        input_data: Any,
        output_data: Any,
        status: str,
        metrics: Optional[Dict[str, Any]] = None,
        reason: Optional[str] = None,
        memory_layer: Optional[str] = None,
        trace_id: Optional[str] = None,
        next_state_id: Optional[str] = None
    ):
        """Log a structured event in JSONL format."""
        level_value = self.verbosity_levels.get(level.lower(), logging.INFO)
        extra = {
            "session_id": session_id,
            "state_id": state_id,
            "layer": layer,
            "step": step,
            "custom_module": custom_module,
            "agent_name": agent_name,
            "action": action,
            "input_data": input_data,
            "output_data": output_data,
            "status": status,
            "metrics": metrics or {},
            "reason": reason,
            "memory_layer": memory_layer,
            "trace_id": trace_id or str(uuid.uuid4()),
            "next_state_id": next_state_id
        }
        with self.queue_lock:
            self.logger.log(level_value, message, extra=extra)

    def log_state_output(
        self,
        level: str,
        message: str,
        session_id: str,
        state_id: str,
        layer: str,
        step: str,
        custom_module: str,
        agent_name: str,
        action: str,
        state_output: StateOutput,
        input_data: Any,
        metrics: Optional[Dict[str, Any]] = None,
        memory_layer: Optional[str] = None,
        trace_id: Optional[str] = None,
        next_state_id: Optional[str] = None
    ):
        """Log a StateOutput object directly."""
        level_value = self.verbosity_levels.get(level.lower(), logging.INFO)
        extra = {
            "session_id": session_id,
            "state_id": state_id,
            "layer": layer,
            "step": step,
            "custom_module": custom_module,
            "agent_name": agent_name,
            "action": action,
            "input_data": input_data,
            "output_data": {
                "status": state_output.status,
                "data": state_output.data,
                "metadata": state_output.metadata
            },
            "status": state_output.status,
            "metrics": metrics or {},
            "reason": None,
            "memory_layer": memory_layer,
            "trace_id": trace_id or str(uuid.uuid4()),
            "next_state_id": next_state_id
        }
        with self.queue_lock:
            self.logger.log(level_value, message, extra=extra)

    def log_decorator(self, func):
        """Decorator to automatically log function execution."""
        sig = inspect.signature(func)
        def wrapper(*args, **kwargs):
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            session_id = bound_args.arguments.get("session_id", str(uuid.uuid4()))
            state_id = bound_args.arguments.get("state_id", "unknown")
            trace_id = str(uuid.uuid4())
            start_time = datetime.now(timezone.utc)

            try:
                result = func(*args, **kwargs)
                duration_ms = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
                level = "info"
                message = f"Executed {func.__name__}"

                if isinstance(result, StateOutput):
                    self.log_state_output(
                        level=level,
                        message=message,
                        session_id=session_id,
                        state_id=state_id,
                        layer=kwargs.get("layer", "unknown"),
                        step=kwargs.get("step", func.__name__),
                        custom_module=func.__module__,
                        agent_name=kwargs.get("agent_name", func.__qualname__),
                        action=func.__name__,
                        state_output=result,
                        input_data=bound_args.arguments,
                        metrics={"duration_ms": duration_ms},
                        trace_id=trace_id
                    )
                else:
                    self.log(
                        level=level,
                        message=message,
                        session_id=session_id,
                        state_id=state_id,
                        layer=kwargs.get("layer", "unknown"),
                        step=kwargs.get("step", func.__name__),
                        custom_module=func.__module__,
                        agent_name=kwargs.get("agent_name", func.__qualname__),
                        action=func.__name__,
                        input_data=bound_args.arguments,
                        output_data=result,
                        status="success",
                        metrics={"duration_ms": duration_ms},
                        trace_id=trace_id
                    )
                return result

            except Exception as e:
                duration_ms = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
                self.log(
                    level="error",
                    message=f"Error in {func.__name__}: {str(e)}",
                    session_id=session_id,
                    state_id=state_id,
                    layer=kwargs.get("layer", "unknown"),
                    step=kwargs.get("step", func.__name__),
                    custom_module=func.__module__,
                    agent_name=kwargs.get("agent_name", func.__qualname__),
                    action=func.__name__,
                    input_data=bound_args.arguments,
                    output_data=None,
                    status="fail",
                    reason=str(e),
                    metrics={"duration_ms": duration_ms},
                    trace_id=trace_id
                )
                raise

        return wrapper

    def flush(self):
        """Flush all logging handlers to ensure logs are written."""
        for handler in self.logger.handlers[:]:
            handler.flush()
            if isinstance(handler, logging.handlers.QueueHandler):
                handler.handler.flush()
                start_time = time.time()
                while not handler.queue.empty() and time.time() - start_time < 20:  # Increased timeout
                    time.sleep(0.01)
                qsize = handler.queue.qsize()
                file_size = os.path.getsize(handler.handler.baseFilename) if os.path.exists(handler.handler.baseFilename) else 0
                # Force fsync to ensure writes are committed
                try:
                    with open(handler.handler.baseFilename, 'a') as f:
                        os.fsync(f.fileno())
                except (OSError, PermissionError) as e:
                    self.logger.error(f"Failed to fsync {handler.handler.baseFilename}: {str(e)}")
                log_type = handler.handler.baseFilename.split(os.sep)[-2]
                self.logger.debug(f"Flushed handler for {handler.handler.baseFilename}, queue size: {qsize}, file size: {file_size} bytes, processed records: {self.record_count[log_type]}")
                if qsize > 0:
                    self.logger.warning(f"Queue not fully emptied for {handler.handler.baseFilename}, remaining: {qsize}")

    def close(self):
        """Close all logging handlers and stop queue listeners."""
        handlers = self.logger.handlers[:]
        for listener in self.queue_listeners[:]:
            try:
                listener.queue.put_nowait(None)
                listener.stop()
                if listener._thread:
                    listener._thread.join(timeout=10)  # Wait for thread to terminate
                for h in listener.handlers:
                    h.flush()
                    try:
                        with open(h.baseFilename, 'a') as f:
                            os.fsync(f.fileno())
                    except (OSError, PermissionError) as e:
                        self.logger.error(f"Failed to fsync {h.baseFilename}: {str(e)}")
                    h.close()
                queue_handler = next((h for h in handlers if isinstance(h, logging.handlers.QueueHandler) and h.queue is listener.queue), None)
                if queue_handler:
                    start_time = time.time()
                    while not queue_handler.queue.empty() and time.time() - start_time < 20:  # Increased timeout
                        time.sleep(0.01)
                    qsize = queue_handler.queue.qsize()
                    file_size = os.path.getsize(queue_handler.handler.baseFilename) if os.path.exists(queue_handler.handler.baseFilename) else 0
                    thread_name = listener._thread.name if listener._thread else "None"
                    log_type = queue_handler.handler.baseFilename.split(os.sep)[-2]
                    self.logger.debug(f"Stopped QueueListener for {queue_handler.handler.baseFilename}, queue size: {qsize}, file size: {file_size} bytes, thread: {thread_name}, processed records: {self.record_count[log_type]}")
                else:
                    self.logger.debug("No matching QueueHandler found for listener")
            except Exception as e:
                self.logger.error(f"Error stopping QueueListener: {str(e)}")
        for handler in handlers:
            try:
                handler.flush()
                if isinstance(handler, logging.handlers.QueueHandler):
                    handler.handler.flush()
                    try:
                        with open(handler.handler.baseFilename, 'a') as f:
                            os.fsync(f.fileno())
                    except (OSError, PermissionError) as e:
                        self.logger.error(f"Failed to fsync {handler.handler.baseFilename}: {str(e)}")
                    handler.handler.close()
                handler.close()
            except Exception as e:
                self.logger.error(f"Error closing handler: {str(e)}")
        self.logger.handlers.clear()
        self.queue_listeners.clear()