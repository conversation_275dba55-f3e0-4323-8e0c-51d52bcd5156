import logging
import logging.handlers
import json
import os
import queue
import time
import inspect
import threading
import os.path
import atexit
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import uuid

class StateOutput:
    """Mock StateOutput class for demonstration purposes."""
    def __init__(self, status: str, data: Any, metadata: Optional[Dict] = None):
        self.status = status
        self.data = data
        self.metadata = metadata or {}

class JsonFormatter(logging.Formatter):
    """Custom formatter for JSONL output."""
    def format(self, record):
        try:
            log_entry = {
                "timestamp": self.formatTime(record, "%Y-%m-%dT%H:%M:%SZ"),
                "session_id": getattr(record, "session_id", str(uuid.uuid4())),
                "state_id": getattr(record, "state_id", "unknown"),
                "layer": getattr(record, "layer", "unknown"),
                "step": getattr(record, "step", getattr(record, "funcName", "unknown")),
                "custom_module": getattr(record, "custom_module", getattr(record, "module", "unknown")),
                "agent_name": getattr(record, "agent_name", record.name),
                "action": getattr(record, "action", getattr(record, "funcName", "unknown")),
                "input": getattr(record, "input_data", None),
                "output": getattr(record, "output_data", None),
                "status": getattr(record, "status", "success" if record.levelno < 40 else "fail"),
                "metrics": getattr(record, "metrics", {}),
                "reason": getattr(record, "reason", None) or (record.getMessage() if record.levelno >= 40 else None),
                "memory_layer": getattr(record, "memory_layer", None),
                "trace_id": getattr(record, "trace_id", str(uuid.uuid4())),
                "next_state_id": getattr(record, "next_state_id", None)
            }
            log_entry = {k: v for k, v in log_entry.items() if v is not None}
            return json.dumps(log_entry, ensure_ascii=False)
        except Exception:
            # Fallback to simple format if JSON formatting fails
            return f"{record.levelname}: {record.getMessage()}"

class Logger:
    """Unified JSONL logger wrapping Python's logging module."""

    def __init__(self, log_dir: str = "logs", verbosity: str = "info", max_bytes: int = 10**6, backup_count: int = 5):
        """Initialize logger with log directory, verbosity, and rotation settings."""
        self.log_dir = log_dir
        self.verbosity_levels = {
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warn": logging.WARNING,
            "error": logging.ERROR,
            "audit": logging.CRITICAL
        }
        self.verbosity = self.verbosity_levels.get(verbosity.lower(), logging.INFO)
        self.queue_listeners = []
        self.queue_lock = threading.RLock()  # Use RLock to prevent deadlocks
        self.record_count = { "conversations": 0, "metrics": 0, "errors": 0 }
        self._closed = False

        # Create directories
        os.makedirs(os.path.join(log_dir, "conversations"), exist_ok=True)
        os.makedirs(os.path.join(log_dir, "metrics"), exist_ok=True)
        os.makedirs(os.path.join(log_dir, "errors"), exist_ok=True)

        # Create logger with unique name
        self.logger = logging.getLogger(f"voice_agent_{id(self)}")
        self.logger.handlers.clear()
        self.logger.setLevel(self.verbosity)
        self.logger.propagate = False  # Prevent propagation to root logger

        # Simple console handler without JSON formatting to avoid recursion
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(logging.WARNING)  # Only show warnings and errors on console
        self.logger.addHandler(console_handler)

        # JSON formatter for file handlers
        formatter = JsonFormatter()

        # Create queue handlers with proper thread-safe listeners
        self._setup_file_handlers(log_dir, max_bytes, backup_count, formatter)

    def _setup_file_handlers(self, log_dir: str, max_bytes: int, backup_count: int, formatter):
        """Set up file handlers with queue listeners for thread-safe logging."""
        import queue

        # Define handler configurations
        handler_configs = [
            ("conversations", logging.INFO),
            ("metrics", logging.INFO),
            ("errors", logging.ERROR)
        ]

        for log_type, level in handler_configs:
            # Create rotating file handler
            file_path = os.path.join(log_dir, log_type, f"{log_type}.jsonl")
            rotating_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=max_bytes,
                backupCount=backup_count
            )
            rotating_handler.setLevel(level)
            rotating_handler.setFormatter(formatter)

            # Create queue and queue handler
            log_queue = queue.Queue(-1)
            queue_handler = logging.handlers.QueueHandler(log_queue)
            queue_handler.setLevel(level)
            queue_handler.setFormatter(formatter)

            # Store reference to the file handler for later access
            queue_handler.handler = rotating_handler

            # Add queue handler to logger
            self.logger.addHandler(queue_handler)

            # Create and start queue listener
            listener = logging.handlers.QueueListener(
                log_queue,
                rotating_handler,
                respect_handler_level=True
            )
            listener.start()
            self.queue_listeners.append(listener)

    def log(
        self,
        level: str,
        message: str,
        session_id: str,
        state_id: str,
        layer: str,
        step: str,
        custom_module: str,
        agent_name: str,
        action: str,
        input_data: Any,
        output_data: Any,
        status: str,
        metrics: Optional[Dict[str, Any]] = None,
        reason: Optional[str] = None,
        memory_layer: Optional[str] = None,
        trace_id: Optional[str] = None,
        next_state_id: Optional[str] = None
    ):
        """Log a structured event in JSONL format."""
        level_value = self.verbosity_levels.get(level.lower(), logging.INFO)
        extra = {
            "session_id": session_id,
            "state_id": state_id,
            "layer": layer,
            "step": step,
            "custom_module": custom_module,
            "agent_name": agent_name,
            "action": action,
            "input_data": input_data,
            "output_data": output_data,
            "status": status,
            "metrics": metrics or {},
            "reason": reason,
            "memory_layer": memory_layer,
            "trace_id": trace_id or str(uuid.uuid4()),
            "next_state_id": next_state_id
        }
        # Use the lock to ensure thread-safe logging
        with self.queue_lock:
            self.logger.log(level_value, message, extra=extra)

    def log_state_output(
        self,
        level: str,
        message: str,
        session_id: str,
        state_id: str,
        layer: str,
        step: str,
        custom_module: str,
        agent_name: str,
        action: str,
        state_output: StateOutput,
        input_data: Any,
        metrics: Optional[Dict[str, Any]] = None,
        memory_layer: Optional[str] = None,
        trace_id: Optional[str] = None,
        next_state_id: Optional[str] = None
    ):
        """Log a StateOutput object directly."""
        level_value = self.verbosity_levels.get(level.lower(), logging.INFO)
        extra = {
            "session_id": session_id,
            "state_id": state_id,
            "layer": layer,
            "step": step,
            "custom_module": custom_module,
            "agent_name": agent_name,
            "action": action,
            "input_data": input_data,
            "output_data": {
                "status": state_output.status,
                "data": state_output.data,
                "metadata": state_output.metadata
            },
            "status": state_output.status,
            "metrics": metrics or {},
            "reason": None,
            "memory_layer": memory_layer,
            "trace_id": trace_id or str(uuid.uuid4()),
            "next_state_id": next_state_id
        }
        # Use the lock to ensure thread-safe logging
        with self.queue_lock:
            self.logger.log(level_value, message, extra=extra)

    def log_decorator(self, func):
        """Decorator to automatically log function execution."""
        sig = inspect.signature(func)
        def wrapper(*args, **kwargs):
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            session_id = bound_args.arguments.get("session_id", str(uuid.uuid4()))
            state_id = bound_args.arguments.get("state_id", "unknown")
            trace_id = str(uuid.uuid4())
            start_time = datetime.now(timezone.utc)

            try:
                result = func(*args, **kwargs)
                duration_ms = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
                level = "info"
                message = f"Executed {func.__name__}"

                if isinstance(result, StateOutput):
                    self.log_state_output(
                        level=level,
                        message=message,
                        session_id=session_id,
                        state_id=state_id,
                        layer=kwargs.get("layer", "unknown"),
                        step=kwargs.get("step", func.__name__),
                        custom_module=func.__module__,
                        agent_name=kwargs.get("agent_name", func.__qualname__),
                        action=func.__name__,
                        state_output=result,
                        input_data=bound_args.arguments,
                        metrics={"duration_ms": duration_ms},
                        trace_id=trace_id
                    )
                else:
                    self.log(
                        level=level,
                        message=message,
                        session_id=session_id,
                        state_id=state_id,
                        layer=kwargs.get("layer", "unknown"),
                        step=kwargs.get("step", func.__name__),
                        custom_module=func.__module__,
                        agent_name=kwargs.get("agent_name", func.__qualname__),
                        action=func.__name__,
                        input_data=bound_args.arguments,
                        output_data=result,
                        status="success",
                        metrics={"duration_ms": duration_ms},
                        trace_id=trace_id
                    )
                return result

            except Exception as e:
                duration_ms = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
                self.log(
                    level="error",
                    message=f"Error in {func.__name__}: {str(e)}",
                    session_id=session_id,
                    state_id=state_id,
                    layer=kwargs.get("layer", "unknown"),
                    step=kwargs.get("step", func.__name__),
                    custom_module=func.__module__,
                    agent_name=kwargs.get("agent_name", func.__qualname__),
                    action=func.__name__,
                    input_data=bound_args.arguments,
                    output_data=None,
                    status="fail",
                    reason=str(e),
                    metrics={"duration_ms": duration_ms},
                    trace_id=trace_id
                )
                raise

        return wrapper

    def flush(self):
        """Flush all logging handlers to ensure logs are written."""
        if self._closed:
            return

        with self.queue_lock:
            # First, flush all handlers
            for handler in self.logger.handlers[:]:
                try:
                    if hasattr(handler, 'flush'):
                        handler.flush()
                except Exception:
                    pass  # Ignore flush errors

            # Wait for all queues to be processed
            max_wait_time = 10  # Reasonable timeout
            for handler in self.logger.handlers[:]:
                if isinstance(handler, logging.handlers.QueueHandler):
                    try:
                        # Flush the underlying file handler
                        if hasattr(handler, 'handler') and hasattr(handler.handler, 'flush'):
                            handler.handler.flush()

                        # Wait for queue to be processed with timeout
                        start_time = time.time()
                        while not handler.queue.empty() and time.time() - start_time < max_wait_time:
                            time.sleep(0.1)
                            # Break if taking too long to avoid hanging
                            if time.time() - start_time > 5:
                                break

                        # Final flush and sync
                        if hasattr(handler, 'handler') and hasattr(handler.handler, 'flush'):
                            handler.handler.flush()

                        # Force fsync to ensure writes are committed
                        if hasattr(handler, 'handler') and hasattr(handler.handler, 'baseFilename'):
                            try:
                                if os.path.exists(handler.handler.baseFilename):
                                    with open(handler.handler.baseFilename, 'a') as f:
                                        os.fsync(f.fileno())
                            except (OSError, PermissionError):
                                pass  # Ignore fsync errors
                    except Exception:
                        pass  # Ignore any errors during flush

    def close(self):
        """Close all logging handlers and stop queue listeners."""
        if self._closed:
            return

        self._closed = True

        try:
            # First, flush any pending logs
            self.flush()

            # Stop all queue listeners
            for listener in self.queue_listeners[:]:
                try:
                    # Signal the listener to stop
                    if hasattr(listener, 'queue'):
                        try:
                            listener.queue.put_nowait(None)
                        except:
                            pass

                    # Stop the listener
                    listener.stop()

                    # Wait for the listener thread to terminate
                    if hasattr(listener, '_thread') and listener._thread and listener._thread.is_alive():
                        listener._thread.join(timeout=5)  # Shorter timeout to avoid hanging

                    # Close handlers managed by this listener
                    if hasattr(listener, 'handlers'):
                        for h in listener.handlers:
                            try:
                                if hasattr(h, 'flush'):
                                    h.flush()
                                if hasattr(h, 'close'):
                                    h.close()
                            except Exception:
                                pass  # Ignore close errors

                except Exception:
                    pass  # Ignore listener stop errors

            # Close all remaining handlers
            for handler in self.logger.handlers[:]:
                try:
                    if hasattr(handler, 'flush'):
                        handler.flush()
                    if hasattr(handler, 'close'):
                        handler.close()
                except Exception:
                    pass  # Ignore close errors

            # Clear all references
            self.logger.handlers.clear()
            self.queue_listeners.clear()

        except Exception:
            pass  # Ignore any errors during close