import logging
import logging.handlers
import json
import os
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import uuid

class StateOutput:
    """Mock StateOutput class for demonstration purposes."""
    def __init__(self, status: str, data: Any, metadata: Optional[Dict] = None):
        self.status = status
        self.data = data
        self.metadata = metadata or {}

class JsonFormatter(logging.Formatter):
    """Custom formatter for JSONL output."""
    def format(self, record):
        try:
            log_entry = {
                "timestamp": self.formatTime(record, "%Y-%m-%dT%H:%M:%SZ"),
                "session_id": getattr(record, "session_id", str(uuid.uuid4())),
                "state_id": getattr(record, "state_id", "unknown"),
                "layer": getattr(record, "layer", "unknown"),
                "step": getattr(record, "step", "unknown"),
                "custom_module": getattr(record, "custom_module", "unknown"),
                "agent_name": getattr(record, "agent_name", record.name),
                "action": getattr(record, "action", "unknown"),
                "input": getattr(record, "input_data", None),
                "output": getattr(record, "output_data", None),
                "status": getattr(record, "status", "success" if record.levelno < 40 else "fail"),
                "metrics": getattr(record, "metrics", {}),
                "reason": getattr(record, "reason", None),
                "memory_layer": getattr(record, "memory_layer", None),
                "trace_id": getattr(record, "trace_id", str(uuid.uuid4())),
                "next_state_id": getattr(record, "next_state_id", None)
            }
            log_entry = {k: v for k, v in log_entry.items() if v is not None}
            return json.dumps(log_entry, ensure_ascii=False)
        except Exception:
            return f"{record.levelname}: {record.getMessage()}"

class Logger:
    """Simplified JSONL logger for testing."""
    
    def __init__(self, log_dir: str = "logs", verbosity: str = "info", max_bytes: int = 10**6, backup_count: int = 5):
        """Initialize logger with direct file handlers (no queues for now)."""
        self.log_dir = log_dir
        self.verbosity_levels = {
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warn": logging.WARNING,
            "error": logging.ERROR,
            "audit": logging.CRITICAL
        }
        self.verbosity = self.verbosity_levels.get(verbosity.lower(), logging.INFO)
        self._lock = threading.RLock()
        self._closed = False

        # Create directories
        os.makedirs(os.path.join(log_dir, "conversations"), exist_ok=True)
        os.makedirs(os.path.join(log_dir, "metrics"), exist_ok=True)
        os.makedirs(os.path.join(log_dir, "errors"), exist_ok=True)

        # Create logger with unique name
        self.logger = logging.getLogger(f"voice_agent_{id(self)}")
        self.logger.handlers.clear()
        self.logger.setLevel(self.verbosity)
        self.logger.propagate = False

        # JSON formatter for file handlers
        formatter = JsonFormatter()

        # Create direct file handlers (no queues for simplicity)
        self._setup_direct_handlers(log_dir, max_bytes, backup_count, formatter)

    def _setup_direct_handlers(self, log_dir: str, max_bytes: int, backup_count: int, formatter):
        """Set up direct file handlers without queues."""
        
        handler_configs = [
            ("conversations", logging.INFO),
            ("metrics", logging.INFO),
            ("errors", logging.ERROR)
        ]
        
        for log_type, level in handler_configs:
            file_path = os.path.join(log_dir, log_type, f"{log_type}.jsonl")
            handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=max_bytes,
                backupCount=backup_count
            )
            handler.setLevel(level)
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def log(self, level: str, message: str, session_id: str, state_id: str, layer: str, 
            step: str, custom_module: str, agent_name: str, action: str, 
            input_data: Any, output_data: Any, status: str, 
            metrics: Optional[Dict[str, Any]] = None, reason: Optional[str] = None,
            memory_layer: Optional[str] = None, trace_id: Optional[str] = None,
            next_state_id: Optional[str] = None):
        """Log a structured event in JSONL format."""
        if self._closed:
            return
            
        level_value = self.verbosity_levels.get(level.lower(), logging.INFO)
        extra = {
            "session_id": session_id,
            "state_id": state_id,
            "layer": layer,
            "step": step,
            "custom_module": custom_module,
            "agent_name": agent_name,
            "action": action,
            "input_data": input_data,
            "output_data": output_data,
            "status": status,
            "metrics": metrics or {},
            "reason": reason,
            "memory_layer": memory_layer,
            "trace_id": trace_id or str(uuid.uuid4()),
            "next_state_id": next_state_id
        }
        
        with self._lock:
            self.logger.log(level_value, message, extra=extra)

    def log_state_output(self, level: str, message: str, session_id: str, state_id: str,
                        layer: str, step: str, custom_module: str, agent_name: str,
                        action: str, state_output: StateOutput, input_data: Any,
                        metrics: Optional[Dict[str, Any]] = None, memory_layer: Optional[str] = None,
                        trace_id: Optional[str] = None, next_state_id: Optional[str] = None):
        """Log a StateOutput object directly."""
        self.log(
            level=level,
            message=message,
            session_id=session_id,
            state_id=state_id,
            layer=layer,
            step=step,
            custom_module=custom_module,
            agent_name=agent_name,
            action=action,
            input_data=input_data,
            output_data={
                "status": state_output.status,
                "data": state_output.data,
                "metadata": state_output.metadata
            },
            status=state_output.status,
            metrics=metrics,
            memory_layer=memory_layer,
            trace_id=trace_id,
            next_state_id=next_state_id
        )

    def log_decorator(self, func):
        """Decorator to automatically log function execution."""
        import inspect
        sig = inspect.signature(func)
        def wrapper(*args, **kwargs):
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            session_id = bound_args.arguments.get("session_id", str(uuid.uuid4()))
            state_id = bound_args.arguments.get("state_id", "unknown")
            trace_id = str(uuid.uuid4())
            start_time = datetime.now(timezone.utc)

            try:
                result = func(*args, **kwargs)
                duration_ms = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
                
                if isinstance(result, StateOutput):
                    self.log_state_output(
                        level="info",
                        message=f"Executed {func.__name__}",
                        session_id=session_id,
                        state_id=state_id,
                        layer=kwargs.get("layer", "unknown"),
                        step=kwargs.get("step", func.__name__),
                        custom_module=func.__module__,
                        agent_name=kwargs.get("agent_name", func.__qualname__),
                        action=func.__name__,
                        state_output=result,
                        input_data=bound_args.arguments,
                        metrics={"duration_ms": duration_ms},
                        trace_id=trace_id
                    )
                else:
                    self.log(
                        level="info",
                        message=f"Executed {func.__name__}",
                        session_id=session_id,
                        state_id=state_id,
                        layer=kwargs.get("layer", "unknown"),
                        step=kwargs.get("step", func.__name__),
                        custom_module=func.__module__,
                        agent_name=kwargs.get("agent_name", func.__qualname__),
                        action=func.__name__,
                        input_data=bound_args.arguments,
                        output_data=result,
                        status="success",
                        metrics={"duration_ms": duration_ms},
                        trace_id=trace_id
                    )
                return result

            except Exception as e:
                duration_ms = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
                self.log(
                    level="error",
                    message=f"Error in {func.__name__}: {str(e)}",
                    session_id=session_id,
                    state_id=state_id,
                    layer=kwargs.get("layer", "unknown"),
                    step=kwargs.get("step", func.__name__),
                    custom_module=func.__module__,
                    agent_name=kwargs.get("agent_name", func.__qualname__),
                    action=func.__name__,
                    input_data=bound_args.arguments,
                    output_data=None,
                    status="fail",
                    reason=str(e),
                    metrics={"duration_ms": duration_ms},
                    trace_id=trace_id
                )
                raise

        return wrapper

    def flush(self):
        """Flush all logging handlers."""
        if self._closed:
            return
            
        with self._lock:
            for handler in self.logger.handlers[:]:
                try:
                    if hasattr(handler, 'flush'):
                        handler.flush()
                except Exception:
                    pass

    def close(self):
        """Close all logging handlers."""
        if self._closed:
            return
            
        self._closed = True
        
        try:
            self.flush()
            
            for handler in self.logger.handlers[:]:
                try:
                    if hasattr(handler, 'close'):
                        handler.close()
                except Exception:
                    pass

            self.logger.handlers.clear()
            
        except Exception:
            pass
