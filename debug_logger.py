#!/usr/bin/env python3

import os
import sys
import threading
import time
import tempfile
import shutil

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.logger import Logger

def test_simple_logging():
    """Test basic logging functionality"""
    print("Testing basic logging...")
    
    # Create a temporary directory for logs
    log_dir = tempfile.mkdtemp(prefix="test_logs_")
    
    try:
        logger = Logger(log_dir=log_dir, verbosity="debug", max_bytes=10000, backup_count=2)
        
        # Test a simple log
        logger.log(
            level="info",
            message="Test message",
            session_id="test_session",
            state_id="test_state",
            layer="test",
            step="test_step",
            custom_module="test_module",
            agent_name="TestAgent",
            action="test_action",
            input_data={"test": "input"},
            output_data={"test": "output"},
            status="success"
        )
        
        print("Basic log created successfully")
        
        # Flush and close
        logger.flush()
        logger.close()
        
        # Check if log files were created
        metrics_file = os.path.join(log_dir, "metrics", "metrics.jsonl")
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r') as f:
                content = f.read()
                print(f"Metrics file content: {content[:200]}...")
        else:
            print("Metrics file not found")
            
        print("Basic logging test completed successfully")
        
    except Exception as e:
        print(f"Basic logging test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        if os.path.exists(log_dir):
            shutil.rmtree(log_dir, ignore_errors=True)

def test_concurrent_logging():
    """Test concurrent logging with multiple threads"""
    print("Testing concurrent logging...")
    
    # Create a temporary directory for logs
    log_dir = tempfile.mkdtemp(prefix="test_concurrent_logs_")
    
    try:
        logger = Logger(log_dir=log_dir, verbosity="debug", max_bytes=10000, backup_count=2)
        
        def log_thread(thread_id):
            """Function to run in each thread"""
            for i in range(5):  # Reduced number for debugging
                logger.log(
                    level="info",
                    message=f"Thread {thread_id} message {i}",
                    session_id=f"session_{thread_id}",
                    state_id="test_state",
                    layer="test",
                    step="test_step",
                    custom_module="test_module",
                    agent_name="TestAgent",
                    action="test_action",
                    input_data={"thread": thread_id, "iteration": i},
                    output_data={"result": "test"},
                    status="success"
                )
                time.sleep(0.01)  # Small delay to simulate work
        
        # Create and start threads
        num_threads = 3  # Reduced number for debugging
        threads = []
        
        print(f"Starting {num_threads} threads...")
        for i in range(num_threads):
            thread = threading.Thread(target=log_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        print("All threads completed, flushing...")
        
        # Flush and wait
        logger.flush()
        time.sleep(2)  # Give time for async processing
        
        print("Closing logger...")
        logger.close()
        
        # Check results
        metrics_file = os.path.join(log_dir, "metrics", "metrics.jsonl")
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r') as f:
                lines = f.readlines()
                print(f"Found {len(lines)} log entries in metrics file")
                expected = num_threads * 5
                if len(lines) == expected:
                    print("✓ Concurrent logging test PASSED")
                else:
                    print(f"✗ Concurrent logging test FAILED: expected {expected}, got {len(lines)}")
        else:
            print("✗ Metrics file not found")
            
    except Exception as e:
        print(f"Concurrent logging test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        if os.path.exists(log_dir):
            shutil.rmtree(log_dir, ignore_errors=True)

if __name__ == "__main__":
    print("Starting logger debug tests...")
    test_simple_logging()
    print("\n" + "="*50 + "\n")
    test_concurrent_logging()
    print("Debug tests completed.")
