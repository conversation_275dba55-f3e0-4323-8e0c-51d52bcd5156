#!/usr/bin/env python3

import os
import sys
import tempfile
import shutil

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Starting minimal test...")

try:
    print("Importing Logger...")
    from core.logger import Logger
    print("Logger imported successfully")
    
    print("Creating temporary directory...")
    log_dir = tempfile.mkdtemp(prefix="minimal_test_")
    print(f"Log directory: {log_dir}")
    
    print("Creating Logger instance...")
    logger = Logger(log_dir=log_dir, verbosity="debug", max_bytes=10000, backup_count=2)
    print("Logger created successfully")
    
    print("Test completed successfully")
    
except Exception as e:
    print(f"Test failed: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("Cleaning up...")
    if 'log_dir' in locals() and os.path.exists(log_dir):
        shutil.rmtree(log_dir, ignore_errors=True)
    print("Done.")
