#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Testing simple logger import...")

try:
    from core.logger_simple import Logger
    print("✓ Simple Logger imported successfully")
    
    print("Creating logger...")
    logger = Logger(log_dir="test_logs_simple", verbosity="info")
    print("✓ Logger created successfully")
    
    print("Testing basic log...")
    logger.log(
        level="info",
        message="Test message",
        session_id="test",
        state_id="test",
        layer="test",
        step="test",
        custom_module="test",
        agent_name="test",
        action="test",
        input_data={},
        output_data={},
        status="success"
    )
    print("✓ Log message sent")
    
    print("Flushing...")
    logger.flush()
    print("✓ Flushed")
    
    print("Closing logger...")
    logger.close()
    print("✓ Logger closed")
    
    print("All tests passed!")
    
except Exception as e:
    print(f"✗ Test failed: {e}")
    import traceback
    traceback.print_exc()
