from pathlib import Path
import sys
import unittest
import os
import json
import shutil
import threading
import time
import logging

project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logger import Logger, StateOutput

class TestLogger(unittest.TestCase):
    def setUp(self):
        """Set up test environment with a temporary log directory."""
        self.log_dir = os.path.join(os.path.expanduser("~"), "voice_agent_test_logs")  # Local directory
        logging.getLogger().handlers.clear()
        logging.getLogger("voice_agent").handlers.clear()
        if os.path.exists(self.log_dir):
            for attempt in range(3):
                try:
                    shutil.rmtree(self.log_dir)
                    break
                except PermissionError:
                    time.sleep(1.0)
        for log_type in ["conversations", "metrics", "errors"]:
            log_path = os.path.join(self.log_dir, log_type)
            os.makedirs(log_path, exist_ok=True)
            log_file = os.path.join(log_path, f"{log_type}.jsonl")
            if os.path.exists(log_file):
                os.remove(log_file)
            for f in os.listdir(log_path):
                if f.startswith(f"{log_type}.jsonl"):
                    try:
                        os.remove(os.path.join(log_path, f))
                    except PermissionError:
                        pass
        self.logger = Logger(log_dir=self.log_dir, verbosity="debug", max_bytes=10000, backup_count=2)
        self.session_id = "test_session_123"
        self.state_id = "test_state_001"
        self.trace_id = "test_trace_456"

    def tearDown(self):
        """Clean up test log directory with retry mechanism."""
        self.logger.close()
        time.sleep(1.0)
        if os.path.exists(self.log_dir):
            for attempt in range(3):
                try:
                    shutil.rmtree(self.log_dir)
                    break
                except PermissionError:
                    time.sleep(1.0)
                    if attempt == 2:
                        for root, _, files in os.walk(self.log_dir):
                            for f in files:
                                try:
                                    os.unlink(os.path.join(root, f))
                                except PermissionError:
                                    pass
                        try:
                            shutil.rmtree(self.log_dir)
                        except PermissionError as e:
                            print(f"Failed to delete {self.log_dir}: {e}")

    def _read_log_file(self, log_type: str) -> list:
        """Read all log entries from a specific log file."""
        log_file = os.path.join(self.log_dir, log_type, f"{log_type}.jsonl")
        if not os.path.exists(log_file):
            return []
        with open(log_file, "r", encoding="utf-8") as f:
            return [json.loads(line) for line in f]

    def test_log_success(self):
        """Test successful log entry with decorator."""
        @self.logger.log_decorator
        def sample_function(session_id, state_id):
            return {"result": "success"}

        sample_function(session_id=self.session_id, state_id=self.state_id)
        self.logger.flush()
        time.sleep(0.3)
        logs = self._read_log_file("metrics")
        self.assertGreater(len(logs), 0)
        log_entry = logs[0]
        self.assertEqual(log_entry["session_id"], self.session_id)
        self.assertEqual(log_entry["state_id"], self.state_id)
        self.assertEqual(log_entry["status"], "success")
        self.assertIn("timestamp", log_entry)
        self.assertIn("metrics", log_entry)

    def test_log_state_output(self):
        """Test logging StateOutput object."""
        state_output = StateOutput(status="success", data={"text": "test"}, metadata={"lang": "en"})
        self.logger.log_state_output(
            level="info",
            message="Test StateOutput",
            session_id=self.session_id,
            state_id=self.state_id,
            layer="workflow",
            step="test_step",
            custom_module="test_module",
            agent_name="TestAgent",
            action="test_action",
            state_output=state_output,
            input_data={"input": "test"},
            trace_id=self.trace_id
        )
        self.logger.flush()
        time.sleep(0.3)
        logs = self._read_log_file("conversations")
        self.assertGreater(len(logs), 0)
        log_entry = logs[0]
        self.assertEqual(log_entry["status"], "success")
        self.assertEqual(log_entry["output"]["data"], {"text": "test"})
        self.assertEqual(log_entry["output"]["metadata"], {"lang": "en"})

    def test_log_error(self):
        """Test error logging with decorator."""
        @self.logger.log_decorator
        def failing_function(session_id, state_id):
            raise ValueError("Test error")

        with self.assertRaises(ValueError):
            failing_function(session_id=self.session_id, state_id=self.state_id)
        self.logger.flush()
        time.sleep(0.3)
        logs = self._read_log_file("errors")
        self.assertGreater(len(logs), 0)
        log_entry = logs[0]
        self.assertEqual(log_entry["status"], "fail")
        self.assertEqual(log_entry["reason"], "Test error")

    def test_verbosity_filtering(self):
        """Test that logs respect verbosity levels."""
        logging.getLogger("voice_agent").handlers.clear()
        logger = Logger(log_dir=self.log_dir, verbosity="error")
        logger.log(
            level="debug",
            message="Should not log",
            session_id=self.session_id,
            state_id=self.state_id,
            layer="test",
            step="test_step",
            custom_module="test_module",
            agent_name="TestAgent",
            action="test_action",
            input_data={},
            output_data={},
            status="success"
        )
        logger.flush()
        time.sleep(0.3)
        logs = self._read_log_file("metrics")
        self.assertEqual(len(logs), 0)

        logger.log(
            level="error",
            message="Should log",
            session_id=self.session_id,
            state_id=self.state_id,
            layer="test",
            step="test_step",
            custom_module="test_module",
            agent_name="TestAgent",
            action="test_action",
            input_data={},
            output_data={},
            status="fail"
        )
        logger.flush()
        time.sleep(0.3)
        logs = self._read_log_file("errors")
        self.assertGreater(len(logs), 0)
        logger.close()

    def test_log_rotation(self):
        """Test log file rotation."""
        for i in range(200):
            self.logger.log(
                level="info",
                message=f"Test log {i}" * 10,
                session_id=self.session_id,
                state_id=self.state_id,
                layer="test",
                step="test_step",
                custom_module="test_module",
                agent_name="TestAgent",
                action="test_action",
                input_data={"index": i},
                output_data={"result": "test"},
                status="success",
                metrics={"duration_ms": 10}
            )
        self.logger.flush()
        time.sleep(0.3)
        files = os.listdir(os.path.join(self.log_dir, "metrics"))
        self.assertTrue(any(f.startswith("metrics.jsonl.") for f in files))

    def test_concurrent_logging(self):
        """Test thread-safe logging."""
        def log_thread(i):
            for _ in range(20):
                self.logger.log(
                    level="info",
                    message=f"Thread {i} log",
                    session_id=f"session_{i}",
                    state_id=self.state_id,
                    layer="test",
                    step="test_step",
                    custom_module="test_module",
                    agent_name="TestAgent",
                    action="test_action",
                    input_data={"thread": i},
                    output_data={"result": "test"},
                    status="success",
                    metrics={"duration_ms": 10}
                )

        threads = [threading.Thread(target=log_thread, args=(i,)) for i in range(5)]
        for t in threads:
            t.start()
        for t in threads:
            t.join()
        self.logger.flush()
        print(f"Active threads after flush: {[t.name for t in threading.enumerate()]}")
        start_time = time.time()
        logs = []
        while len(logs) < 100 and time.time() - start_time < 20:  # Increased timeout
            time.sleep(0.5)
            logs = self._read_log_file("metrics")
            for handler in self.logger.logger.handlers:
                if isinstance(handler, logging.handlers.QueueHandler):
                    qsize = handler.queue.qsize()
                    file_size = os.path.getsize(handler.handler.baseFilename) if os.path.exists(handler.handler.baseFilename) else 0
                    print(f"Queue size for {handler.handler.baseFilename}: {qsize}, file size: {file_size} bytes")
        self.assertEqual(len(logs), 100)
        conv_logs = self._read_log_file("conversations")
        self.assertEqual(len(conv_logs), 100)
        session_ids = {log["session_id"] for log in logs}
        self.assertEqual(len(session_ids), 5)